AUTOBOXING 
UNBOXING 

AUTOBOXING - AUTOMATICALLY WRAP 

SMALLEST DATATYPE - BYTE

Integer

byte->short->int->Long
           float->Double

byte,short can be stored in int but int cannot be stored in byte . for that, we have to do typecasting- typecasting may lead to loss of memory
            parse 

Byte   Byte.parseByte()  Byte.valueOf()
Short   Short.parseSort()  Short.valueOf()
Integer  Integer.parseInt() Integer.valueOf()
Long     Long.parseLong()   Long.valueOf()
Float