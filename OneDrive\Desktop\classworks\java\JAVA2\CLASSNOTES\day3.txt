<PERSON>AV<PERSON> SUPPORTS UNICODE (<PERSON><PERSON><PERSON><PERSON><PERSON> IT IS DEVELOPING STILL DATE) - IT REQUIRES 2 BYTES OF DATA. SO CHAR IN JAVA 
                                                                CONSUMES 2 BYTES OF DATA.
50 keywords 
J<PERSON>A TUTORIAL ORACLE - WEBSITE TO LEARN JAVA  - https://docs.oracle.com/javase/tutorial/

DEFAULT - FOR IMPLEMENTING DEFult method in interfase

1) ABSTRACT - TO CREATE ABSTRACT CLASS
2) ASSERT - FOR TESTING - PASS 2 PARAMS ASSERT ADD(10,6)
3) BOOLEAN - TRUE OR FALSE - PRIMITIVE DATATYPE 
4) BREAK - TO BREAK THE LOOP
5) BYTE (SMALLEST DATATYPE IN JAVA) - 8 BITS
6) CASE - TO CHECK THE CONDITION
7) CATCH - TO CATCH THE EXCEPTION
8) CHAR - 16 BITS - 2 BYTES OF MEMORY  
9) CLASS - TO CREATE A CLASS
10) CONST - TO CREATE A CONSTANT       //NOT USED
11) CONTINUE - TO CONTINUE THE LOOP
12) DEFAULT - TO SET THE DEFAULT VALUE
13) DO - TO CREATE A DO WHILE LOOP
14) DOUBLE - 64 BITS - 8 BYTES OF MEMORY
15) ELSE - TO CHECK THE CONDITION
16)ENUM - TO CREATE A ENUMERATION
17) EXTENDS - TO INHERIT THE PROPERTIES OF PARENT CLASS
18) FINAL - TO CREATE A FINAL VARIABLE - FOR METHOD AND CLASS CANNOT BE CHANGED AND CANNOT BE EXTENDED //DOUBT
19) FINALLY - TO EXECUTE THE CODE WHETHER THE EXCEPTION IS THROWN OR NOT
20) FLOAT - 32 BITS - 4 BYTES OF MEMORY
21) FOR - TO CREATE A FOR LOOP
22) IF - TO CHECK THE CONDITION
23) GOTO - TO JUMP TO A PARTICULAR LINE //NOT USED
24) IMPLEMENTS - TO IMPLEMENT THE INTERFACE
25) IMPORT - TO IMPORT THE PACKAGE
26) INSTANCEOF - TO CHECK THE OBJECT IS INSTANCE OF A CLASS - WHETHER THIS OBJECT BELONGS TO THIS CLASS OR NOT
27) INT - 32 BITS - 4 BYTES OF MEMORY
28) INTERFACE - TO CREATE A INTERFACE
29) LONG - 64 BITS - 8 BYTES OF MEMORY
30) NATIVE - TO CALL THE NATIVE METHOD - METHOD WHICH IS WRITTEN IN OTHER LANGUAGE LIKE C, C++ - GARBAGE COLLECTOR(DESTRUCTOR - NOT USED IN JAVA MARK AND SWEEP ALGOS WERE USED IN PREVIOUS VERSIONS OF JAVA) - PART OF JVM   //GARBAGE FIRST ALGORITHM - RUNS FOR SPECIFIC TIME BY DIVIDING THE MEMORY INTO SMALL CHUNKS AND THEN IT WILL BE COLLECTED BY GARBAGE COLLECTOR - SIMILAR TO MARK AND SWEEP ALIVE - GREEN OTHERWISE GREY
31) NEW - TO CREATE A OBJECT - TO ALLOCATE MEMORY - USES CALLOC (ALLOCATES MEMORY BY CLEARING PREV AND PROVIDE DEFAULT VALUES - 0...)
32) PACKAGE - TO CREATE A PACKAGE
33) PRIVATE - TO ACCESS THE VARIABLE WITHIN THE CLASS
34) PROTECTED - TO ACCESS THE VARIABLE WITHIN THE PACKAGE
35) PUBLIC - TO ACCESS THE VARIABLE FROM ANYWHERE
36)RETURN - TO RETURN THE VALUE FROM THE METHOD
37) SHORT - 16 BITS - 2 BYTES OF MEMORY
38) STATIC - TO CREATE A STATIC VARIABLE - BELONGS TO CLASS NOT OBJECT
39) STRICTFP - TO FOLLOW THE IEEE 754 STANDARD FOR FLOATING POINT ARITHMETIC
40) SUPER - TO CALL THE PARENT CLASS CONSTRUCTOR
41) SWITCH - TO CHECK THE CONDITION
42) SYNCHRONIZED - TO ACHIEVE THREAD SAFETY - TO AVOID RACE CONDITION - TO AVOID DATA INCONSISTENCY - TO AVOID DATA CORRUPTION - TO AVOID DATA LOSS - TO AVOID DATA RACE - TO AVOID DATA RACE CONDITION - BALANCE ACCESS FROM MOBILE,ATM,BANK AT THE SAME TIME - UNTIL ONE PROCESS IS COMPLETED, ANOTHER SHOULD WAIT
43) REFERENCE - TO CREATE A REFERENCE VARIABLE - TO STORE THE ADDRESS OF THE OBJECT
44) THIS - TO ACCESS THE CURRENT OBJECT
45) THROW - TO THROW THE EXCEPTION
46) THROWS - TO DECLARE THE EXCEPTION - TELLS JVM THAT THIS METHOD CAN THROW THIS EXCEPTION -TELLS THE COMPILER THAT THIS METHOD MAY THROW EXCEPTION COMPILER,U TAKE CARE
47) TRANSIENT - TO AVOID SERIALIZATION - TO AVOID SAVING THE VALUE OF THE VARIABLE IN THE FILE - USED IN NETWORKING
48) TRY - TO TEST THE CODE FOR EXCEPTION
49) VOID - TO RETURN NOTHING FROM THE METHOD
50) VOLATILE - TO AVOID CACHING THE VALUE OF THE VARIABLE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE VALUE FROM CACHE - TO AVOID USING THE